import { styled } from "../utils/styled";
import { Pressable, Text, View } from "react-native";

export const Container = styled(View)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const ContentContainer = styled(View)`
  flex: 1;
  align-items: center;
  justify-content: space-between;
  padding: 40px 24px 24px 24px;
`;

export const TopContainer = styled.View`
  flex: 2;
  align-items: center;
  justify-content: center;
  width: 100%;
  background-color: ${({ theme }) => theme.colors.background};
`;

export const LogoContainer = styled(View)`
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 200px;
`;

export const MiddleContainer = styled(View)`
  flex: 1;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 10px;
  background-color: ${({ theme }) => theme.colors.background};
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
  e
`;

export const Question = styled(Text)`
  color: ${({ theme }) => theme.colors.text};
  font-size: 20px;
  text-align: center;
  font-weight: bold;
  margin-bottom: 8px;
`;

export const ButtonsContainer = styled(View)`
  width: 100%;
  gap: 16px;
`;

export const RoleButton = styled(Pressable)<{ isSelected: boolean }>`
  width: 100%;
  padding: 18px 24px;
  border-radius: 25px;
  border-width: 1px;
  border-color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primary : theme.colors.primary};
  background-color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primary : "transparent"};
  align-items: center;
  justify-content: center;
`;

export const RoleButtonText = styled(Text)<{ isSelected: boolean }>`
  color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.white : theme.colors.primary};
  font-size: 16px;
  font-weight: bold;
  text-align: center;
`;

export const LinkButton = styled(Pressable)`
  width: 100%;
  align-items: center;
  justify-content: center;
  padding: 12px;
`;

export const LinkText = styled(Text)`
  color: ${({ theme }) => theme.colors.primary};
  font-weight: 500;
  font-size: 16px;
  text-align: center;
`;
